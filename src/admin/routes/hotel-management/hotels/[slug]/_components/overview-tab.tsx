import React, { useState } from "react";
import { Container, Heading, Text, Badge, Button, Input, Textarea, toast } from "@camped-ai/ui";
import {
  MapPin,
  Phone,
  Mail,
  Globe,
  Hash,
  Clock,
  DollarSign,
  Flag,
  CheckCircle,
  XCircle,
  Tags,
  Bed,
  Hotel,
  FileText,
  Edit,
  X,
  Check,
  Percent
} from "lucide-react";
import { HotelData } from "../../../../../types";

interface OverviewTabProps {
  hotel: HotelData | null;
  getTranslatedValue: (field: string) => string;
  hasEditPermission?: boolean;
  onDetailsUpdate?: (field: string, value: string | string[]) => Promise<void>;
  onDescriptionUpdate?: (newDescription: string) => Promise<void>;
}

const OverviewTab: React.FC<OverviewTabProps> = ({
  hotel,
  getTranslatedValue,
  hasEditPermission = false,
  onDetailsUpdate,
  onDescriptionUpdate,
}) => {
  const [showFullDescription, setShowFullDescription] = useState(false);

  // State for editing description
  const [isEditingDescription, setIsEditingDescription] = useState(false);
  const [editedDescription, setEditedDescription] = useState("");

  // New state for editing hotel details
  const [isEditingDetails, setIsEditingDetails] = useState(false);
  const [editedLocation, setEditedLocation] = useState("");
  const [editedWebsite, setEditedWebsite] = useState("");
  const [editedEmail, setEditedEmail] = useState("");
  const [editedTags, setEditedTags] = useState("");
  const [editedMargin, setEditedMargin] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  // Description edit handlers
  const handleEditDescriptionClick = () => {
    const currentDescription = getTranslatedValue("description") || "";
    setEditedDescription(currentDescription);
    setIsEditingDescription(true);
  };

  const handleCancelDescriptionEdit = () => {
    setIsEditingDescription(false);
    setEditedDescription("");
  };

  const handleSaveDescriptionEdit = async () => {
    if (!onDescriptionUpdate) return;

    setIsSaving(true);
    try {
      await onDescriptionUpdate(editedDescription);
      setIsEditingDescription(false);
      toast.success("Success", {
        description: "Description updated successfully",
      });
    } catch (error) {
      console.error("Error updating description:", error);
      toast.error("Error", {
        description: "Failed to update description. Please try again.",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Edit handlers for hotel details
  const handleEditDetailsClick = () => {
    setEditedLocation(hotel?.location || "");
    setEditedWebsite(hotel?.website || "");
    setEditedEmail(hotel?.email || "");
    const tags = hotel?.tags || [];
    setEditedTags(tags.join(", "));
    setEditedMargin(hotel?.margin?.toString() || "");
    setIsEditingDetails(true);
  };

  const handleCancelDetailsEdit = () => {
    setIsEditingDetails(false);
    setEditedLocation("");
    setEditedWebsite("");
    setEditedEmail("");
    setEditedTags("");
    setEditedMargin("");
  };

  const handleSaveDetailsEdit = async () => {
    if (!onDetailsUpdate) return;

    setIsSaving(true);
    try {
      // Update each field that has changed
      if (editedLocation !== (hotel?.location || "")) {
        await onDetailsUpdate("location", editedLocation);
      }
      if (editedWebsite !== (hotel?.website || "")) {
        await onDetailsUpdate("website", editedWebsite);
      }
      if (editedEmail !== (hotel?.email || "")) {
        await onDetailsUpdate("email", editedEmail);
      }

      // Handle tags
      const currentTags = hotel?.tags || [];
      const newTags = editedTags.split(",").map(tag => tag.trim()).filter(tag => tag);
      const currentTagsStr = currentTags.join(", ");
      if (editedTags !== currentTagsStr) {
        await onDetailsUpdate("tags", newTags);
      }

      // Handle margin
      if (editedMargin !== (hotel?.margin?.toString() || "")) {
        const marginValue = editedMargin === "" ? null : parseFloat(editedMargin);
        await onDetailsUpdate("margin", marginValue);
      }

      setIsEditingDetails(false);
      toast.success("Success", {
        description: "Hotel details updated successfully",
      });
    } catch (error) {
      console.error("Error updating hotel details:", error);
      toast.error("Error", {
        description: "Failed to update hotel details. Please try again.",
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (!hotel) {
    return (
      <Container className="p-6">
        <Text className="text-muted-foreground">Loading hotel details...</Text>
      </Container>
    );
  }

  const description = getTranslatedValue("description") || "No description available for this hotel.";

  // Simple heuristic: if description is longer than ~280 characters, it likely exceeds 4 lines
  // This is more reliable than complex DOM measurements and works well in practice
  const isLongDescription = description.length > 280;

  return (
    <Container className="divide-y p-0">
      {/* Description Section */}
      <div className="px-6 py-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <FileText className="w-5 h-5 text-muted-foreground" />
            <Heading level="h3">Description</Heading>
          </div>
          {hasEditPermission && !isEditingDescription && (
            <Button
              variant="secondary"
              size="small"
              onClick={handleEditDescriptionClick}
            >
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Button>
          )}
        </div>
        {isEditingDescription ? (
          // Edit Mode for Description
          <div className="space-y-4">
            <Textarea
              value={editedDescription}
              onChange={(e) => setEditedDescription(e.target.value)}
              placeholder="Enter hotel description..."
              className="min-h-[120px] text-sm"
              disabled={isSaving}
            />
            <div className="flex justify-end gap-2">
              <Button
                variant="secondary"
                size="small"
                onClick={handleCancelDescriptionEdit}
                disabled={isSaving}
              >
                <X className="w-4 h-4 mr-2" />
                Cancel
              </Button>
              <Button
                variant="primary"
                size="small"
                onClick={handleSaveDescriptionEdit}
                disabled={isSaving}
              >
                <Check className="w-4 h-4 mr-2" />
                {isSaving ? "Saving..." : "Save"}
              </Button>
            </div>
          </div>
        ) : (
          // Read-only Mode for Description
          <>
            <div
              className="text-sm text-foreground leading-relaxed break-words"
              style={{
                display: '-webkit-box',
                WebkitLineClamp: !showFullDescription && isLongDescription ? 4 : 'unset',
                WebkitBoxOrient: 'vertical',
                overflow: !showFullDescription && isLongDescription ? 'hidden' : 'visible'
              }}
            >
              {description}
            </div>

            {isLongDescription && (
              <div className="flex justify-end mt-2">
                <Button
                  variant="ghost"
                  size="small"
                  onClick={() => setShowFullDescription(!showFullDescription)}
                  className="text-blue-600 hover:text-blue-700 p-0 h-auto font-normal"
                >
                  {showFullDescription ? "Show Less" : "Show More"}
                </Button>
              </div>
            )}
          </>
        )}
      </div>

      {/* Hotel Details Section */}
      <div className="px-6 py-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <Hotel className="w-5 h-5 text-muted-foreground" />
            <Heading level="h3">Hotel Details</Heading>
          </div>
          {hasEditPermission && !isEditingDetails && (
            <Button
              variant="secondary"
              size="small"
              onClick={handleEditDetailsClick}
            >
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Button>
          )}
        </div>
        <div className="space-y-6">
          {isEditingDetails ? (
            // Edit Mode for Hotel Details
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Location */}
                <div className="space-y-2">
                  <Text className="text-sm font-medium text-foreground">Location</Text>
                  <Input
                    value={editedLocation}
                    onChange={(e) => setEditedLocation(e.target.value)}
                    placeholder="Enter location..."
                    className="text-sm"
                    disabled={isSaving}
                  />
                </div>

                {/* Website */}
                <div className="space-y-2">
                  <Text className="text-sm font-medium text-foreground">Website</Text>
                  <Input
                    value={editedWebsite}
                    onChange={(e) => setEditedWebsite(e.target.value)}
                    placeholder="Enter website URL..."
                    className="text-sm"
                    disabled={isSaving}
                  />
                </div>

                {/* Email */}
                <div className="space-y-2">
                  <Text className="text-sm font-medium text-foreground">Email</Text>
                  <Input
                    value={editedEmail}
                    onChange={(e) => setEditedEmail(e.target.value)}
                    placeholder="Enter email address..."
                    className="text-sm"
                    disabled={isSaving}
                  />
                </div>
              </div>

              {/* Tags */}
              <div className="space-y-2">
                <Text className="text-sm font-medium text-foreground">Tags</Text>
                <Input
                  value={editedTags}
                  onChange={(e) => setEditedTags(e.target.value)}
                  placeholder="Enter tags separated by commas..."
                  className="text-sm"
                  disabled={isSaving}
                />
                <Text className="text-xs text-muted-foreground">Separate multiple tags with commas</Text>
              </div>

              {/* Margin */}
              <div className="space-y-2">
                <Text className="text-sm font-medium text-foreground">Margin (%)</Text>
                <Input
                  value={editedMargin}
                  onChange={(e) => setEditedMargin(e.target.value)}
                  placeholder="0.00"
                  className="text-sm"
                  disabled={isSaving}
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                />
                <Text className="text-xs text-muted-foreground">Margin percentage for this hotel</Text>
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <Button
                  variant="secondary"
                  onClick={handleCancelDetailsEdit}
                  disabled={isSaving}
                  className="px-4 py-2 text-sm font-medium"
                >
                  <X className="w-4 h-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleSaveDetailsEdit}
                  disabled={isSaving}
                  className="px-4 py-2 text-sm font-medium"
                >
                  <Check className="w-4 h-4 mr-2" />
                  {isSaving ? "Saving..." : "Save"}
                </Button>
              </div>
            </div>
          ) : (
            // Read-only Mode for Hotel Details
            <>
              {/* Basic Details Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Location */}
                {getTranslatedValue("location") && (
                  <div className="flex items-start gap-4 p-3 bg-card rounded-lg border border-border hover:border-muted-foreground/20 transition-colors">
                    <div className="w-10 h-10 rounded-full bg-red-100 dark:bg-red-900/50 flex items-center justify-center flex-shrink-0">
                      <MapPin className="w-5 h-5 text-red-600 dark:text-red-400" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <Text className="text-xs text-muted-foreground mb-1">Location</Text>
                      <Text className="font-medium text-foreground break-words">{getTranslatedValue("location")}</Text>
                    </div>
                  </div>
                )}

                {/* Website */}
                {hotel.website && (
                  <div className="flex items-start gap-4 p-3 bg-card rounded-lg border border-border hover:border-muted-foreground/20 transition-colors">
                    <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center flex-shrink-0">
                      <Globe className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <Text className="text-xs text-muted-foreground mb-1">Website</Text>
                      <a
                        href={hotel.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="font-medium text-blue-600 dark:text-blue-400 hover:underline break-all"
                      >
                        {hotel.website}
                      </a>
                    </div>
                  </div>
                )}

                {/* Email */}
                {hotel.email && (
                  <div className="flex items-start gap-4 p-3 bg-card rounded-lg border border-border hover:border-muted-foreground/20 transition-colors">
                    <div className="w-10 h-10 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center flex-shrink-0">
                      <Mail className="w-5 h-5 text-green-600 dark:text-green-400" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <Text className="text-xs text-muted-foreground mb-1">Email</Text>
                      <a
                        href={`mailto:${hotel.email}`}
                        className="font-medium text-green-600 dark:text-green-400 hover:underline break-all"
                      >
                        {hotel.email}
                      </a>
                    </div>
                  </div>
                )}

                {/* Currency */}
                {hotel.currency && (
                  <div className="flex items-start gap-4 p-3 bg-card rounded-lg border border-border hover:border-muted-foreground/20 transition-colors">
                    <div className="w-10 h-10 rounded-full bg-yellow-100 dark:bg-yellow-900/50 flex items-center justify-center flex-shrink-0">
                      <DollarSign className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <Text className="text-xs text-muted-foreground mb-1">Currency</Text>
                      <Text className="font-medium text-foreground font-mono">{hotel.currency}</Text>
                    </div>
                  </div>
                )}

                {/* Margin */}
                {hotel.margin !== null && hotel.margin !== undefined && (
                  <div className="flex items-start gap-4 p-3 bg-card rounded-lg border border-border hover:border-muted-foreground/20 transition-colors">
                    <div className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900/50 flex items-center justify-center flex-shrink-0">
                      <Percent className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <Text className="text-xs text-muted-foreground mb-1">Margin</Text>
                      <Text className="font-medium text-foreground font-mono">{hotel.margin}%</Text>
                    </div>
                  </div>
                )}
              </div>

              {/* Tags */}
              {hotel.tags && hotel.tags.length > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Tags className="w-4 h-4 text-muted-foreground" />
                    <Text className="text-sm font-medium text-muted-foreground">Tags</Text>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {hotel.tags.map((tag, index) => (
                      <Badge key={index} color="grey">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </Container>
  );
};

export default OverviewTab;
