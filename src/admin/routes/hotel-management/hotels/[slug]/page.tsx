import {
  Container,
  <PERSON><PERSON>,
  Text,
  Badge,
  <PERSON><PERSON>,
  FocusModal,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Drawer,
} from "@camped-ai/ui";
import React, { useEffect, useState, useRef, lazy, Suspense } from "react";
import { useParams, useNavigate, Link } from "react-router-dom";
import { HotelData } from "../../../../types";
import { HotelFormData } from "../../../../components/hotel-form";
import HotelFormModern from "../../../../components/hotel-form-modern";
import { toast, Toaster } from "@camped-ai/ui";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import HotelDashboardMetrics from "../../../../components/hotel/hotel-dashboard-metrics";
import LanguageSelector from "../../../../components/language-selector";
import { useProjectLanguages } from "../../../../hooks/languages/useProjectLanguages";
import { useHotelTranslations } from "../../../../hooks/translations/useHotelTranslations";
import OptimizedImageCarousel from "../../../../components/hotel/optimized-image-carousel";
import {
  MapPin,
  Phone,
  Mail,
  FileText,
  Edit,
  Hotel,
  Image,
  Globe,
  Bed,
  CreditCard,
  Calendar,
  BarChart3,
  Tags,
  Shield,
  ArrowLeft,
  Settings,
  Sparkles,
} from "lucide-react";

import { useHotelTabState } from "../../../../hooks/useHotelTabState";
import "./modal-fix.css";
import "../../../../styles/global-modal-zindex.css";
import "../../../../styles/modern-tabs.css";
import "../../../../styles/cancellation-policy-modal.css";
import Rating from "@mui/material/Rating";
import { useRbac } from "../../../../hooks/use-rbac";
import AdminPageHelmet from "../../../../components/AdminPageHelmet";

// Dynamically import for better performance
const BaileyAITab = lazy(() => import("./_components/bailey-ai-tab"));
const HotelTabs = lazy(() => import("./_components/hotel-tabs"));
const HotelTabContent = lazy(() => import("./_components/hotel-tab-content"));
const OverviewTab = lazy(() => import("./_components/overview-tab"));
const AmenitiesTab = lazy(() => import("./_components/amenities-tab"));
const HotelSettingsTab = lazy(() => import("./_components/settings-tab"));

const HotelDetailPage = () => {
  const { slug } = useParams();
  const { hasPermission, loading: rbacLoading } = useRbac();
  const [hotel, setHotel] = useState<HotelData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const [deleteOpen, setDeleteOpen] = useState(false);
  const [imageGalleryOpen, setImageGalleryOpen] = useState(false);
  const [roomTypes, setRoomTypes] = useState([]);
  const navigate = useNavigate();
  const submitRef = useRef<(() => Promise<void>) | null>(null);

  // Tab state management
  const { activeTab, setActiveTab } = useHotelTabState({
    defaultTab: "overview",
  });

  // Language management
  const { languages: tolgeeLanguages } = useProjectLanguages();
  const [selectedLanguage, setSelectedLanguage] = useState("en"); // Default to English
  const isBaseLanguage = selectedLanguage === "en";

  // Translation management
  const hotelId = hotel?.id || "new";
  const {
    loadTranslations,
    getTranslationKeys,
    translations,
    loading: translationsLoading,
    getArrayTranslation,
  } = useHotelTranslations(hotelId);

  // Helper function to get current translated value for a field
  const getTranslatedValue = (fieldName: string): string => {
    if (isBaseLanguage || !hotel) {
      return (hotel as any)?.[fieldName] || "";
    }

    // For non-base languages, try translated value first, then fallback to base language value
    const translationKeys = getTranslationKeys();
    const key = translationKeys[fieldName];
    const translatedValue = key ? translations[selectedLanguage]?.[key] : "";

    if (translatedValue) {
      return translatedValue;
    }

    // Fallback to base language value
    return (hotel as any)?.[fieldName] || "";
  };

  // Helper function to get translated array values
  const getTranslatedArrayValue = (fieldName: string): string[] => {
    if (isBaseLanguage || !hotel) {
      return (hotel as any)?.[fieldName] || [];
    }

    // For non-base languages, try translated value first, then fallback to base language value
    const translatedValue = getArrayTranslation(
      fieldName as any,
      selectedLanguage
    );

    if (translatedValue && translatedValue.length > 0) {
      return translatedValue;
    }

    // Fallback to base language value
    return (hotel as any)?.[fieldName] || [];
  };

  // Load translations when language changes
  React.useEffect(() => {
    const loadTranslationsForLanguage = async () => {
      // Skip loading for base language or if no hotel ID
      if (isBaseLanguage || !hotel?.id || hotel.id === "new") {
        return;
      }

      console.log(
        `🔄 Loading translations for hotel ${hotel.id} in language: ${selectedLanguage}`
      );

      try {
        await loadTranslations(selectedLanguage);
        console.log(
          `✅ Successfully loaded translations for ${selectedLanguage}`
        );
      } catch (error) {
        console.error(
          `❌ Failed to load translations for ${selectedLanguage}:`,
          error
        );
      }
    };

    loadTranslationsForLanguage();
  }, [selectedLanguage, isBaseLanguage, hotel?.id, loadTranslations]);

  // Enhanced hero image selection with better fallback logic
  const heroImage = React.useMemo(() => {
    if (!hotel?.images || hotel.images.length === 0) {
      return null;
    }

    // First try to find a thumbnail image
    const thumbnailImage = hotel.images.find((img) => img.isThumbnail);
    if (thumbnailImage?.url) {
      console.log("Using thumbnail image:", thumbnailImage.url);
      return thumbnailImage.url;
    }

    // Fallback to first image
    const firstImage = hotel.images[0];
    if (firstImage?.url) {
      console.log("Using first image:", firstImage.url);
      return firstImage.url;
    }

    return null;
  }, [hotel?.images]);

  const fetchHotelDetails = async () => {
    try {
      const hotelResponse = await fetch(
        `/admin/hotel-management/hotels/${slug}`,
        {
          credentials: "include",
        }
      );
      const hotelData = await hotelResponse.json();
      console.log("Raw hotel data from API:", hotelData);

      const processedHotelData = Array.isArray(hotelData?.hotel)
        ? hotelData?.hotel[0]
        : hotelData?.hotel || hotelData;

      console.log("Processed hotel data:", processedHotelData);

      if (!processedHotelData || !processedHotelData.id) {
        console.error("Invalid hotel data - no ID found:", processedHotelData);
        setIsLoading(false);
        return;
      }

      // Fetch hotel images
      const imagesResponse = await fetch(
        `/admin/hotel-management/hotels/${processedHotelData.id}/images`,
        {
          credentials: "include",
        }
      );
      const imagesData = await imagesResponse.json();

      const hotelWithImages = {
        ...processedHotelData,
        images: imagesData.images || [],
      };

      console.log("Final hotel data with images:", hotelWithImages);
      setHotel(hotelWithImages);
      setIsLoading(false);
    } catch (error) {
      console.error("Failed to fetch hotel details:", error);
      setIsLoading(false);
    }
  };

  const fetchRoomTypes = () => {
    fetch(`/admin/room-types`, {
      credentials: "include",
    })
      .then((res) => res.json())
      .then(({ roomTypes: data }) => {
        setRoomTypes(data);
      });
  };

  const handleDeleteHotel = async () => {
    if (!hotel) return;

    try {
      const response = await fetch(`/admin/hotel-management/hotels`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          ids: hotel.id,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to delete hotel");
      }

      toast.success("Success", {
        description: "Hotel deleted successfully",
      });

      // Navigate back to hotels list
      navigate("/hotel-management/hotels");
    } catch (error) {
      console.error("Error deleting hotel:", error);
      toast.error("Error", {
        description:
          error instanceof Error ? error.message : "Failed to delete hotel",
      });
    }
  };

  useEffect(() => {
    fetchHotelDetails();
    fetchRoomTypes();
  }, [slug]);

  if (isLoading) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <div className="h-screen flex items-center justify-center bg-background">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </>
    );
  }

  if (!hotel) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="py-8">
          <div className="text-center py-12 bg-muted rounded-lg">
            <Hotel className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <Text className="text-muted-foreground mb-4">Hotel not found</Text>
            <Button
              variant="primary"
              size="small"
              onClick={() => navigate("/hotel-management/hotels")}
            >
              Back to Hotels
            </Button>
          </div>
        </Container>
      </>
    );
  }

  const handleUpdate = async (data: HotelFormData) => {
    try {
      // Separate files to upload from existing URLs
      const mediaArray = data.media ?? [];
      const filesToUpload = mediaArray.filter(
        (media) => media.file instanceof File
      );
      const existingMedia = mediaArray.filter(
        (media) => media.url && !media.file
      );

      // Prepare data for submission
      const dataWithoutMedia = {
        ...data,
        media: existingMedia, // Include existing media in the payload
      };
      delete dataWithoutMedia.image_ids;

      const response = await fetch(`/admin/hotel-management/hotels`, {
        method: "PUT",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(dataWithoutMedia),
      });

      const responseData = await response.json();

      if (response.ok) {
        // Upload new files if any
        if (filesToUpload.length > 0) {
          const uploadPromises = filesToUpload.map(async (mediaFile) => {
            if (!(mediaFile.file instanceof File)) {
              throw new Error("Invalid file");
            }

            const formData = new FormData();
            formData.append("files", mediaFile.file);

            try {
              const hotelId = data.id;
              if (!hotelId) {
                throw new Error("No hotel ID available for image upload");
              }

              const response = await fetch(
                `/admin/hotel-management/hotels/${hotelId}/upload`,
                {
                  method: "POST",
                  body: formData,
                  credentials: "include",
                }
              );

              if (!response.ok) {
                const errorText = await response.text();
                console.error("Upload error response:", errorText);
                throw new Error(`File upload failed: ${errorText}`);
              }

              const uploadedFiles = await response.json();
              const uploadedFile = uploadedFiles[0];

              return {
                ...uploadedFile,
                isThumbnail: mediaFile.isThumbnail,
              };
            } catch (error) {
              console.error("File upload error:", error);
              throw error;
            }
          });

          await Promise.all(uploadPromises);
        }

        toast.success("Success", {
          description: "Hotel updated successfully",
        });

        // Refresh hotel details
        fetchHotelDetails();
        return true;
      } else {
        toast.error("Error", {
          description: responseData.message || "Failed to update hotel",
        });
        return false;
      }
    } catch (error) {
      toast.error("Error", {
        description: "An unexpected error occurred",
      });
      console.error("Failed to update hotel:", error);
      return false;
    }
  };

  const handleDetailsUpdate = async (
    field: string,
    value: string | string[]
  ) => {
    if (!hotel) return;

    try {
      const updateData: any = {
        id: hotel.id,
        [field]: value,
      };

      const response = await fetch(`/admin/hotel-management/hotels`, {
        method: "PUT",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to update ${field}`);
      }

      // Update the local hotel state
      setHotel((prev) => (prev ? { ...prev, [field]: value } : null));

      // Refresh hotel details to ensure consistency
      fetchHotelDetails();
    } catch (error) {
      console.error(`Error updating ${field}:`, error);
      throw error;
    }
  };

  const handleDescriptionUpdate = async (newDescription: string) => {
    if (!hotel) return;

    try {
      const response = await fetch(`/admin/hotel-management/hotels`, {
        method: "PUT",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id: hotel.id,
          description: newDescription,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || "Failed to update description");
      }

      // Update the local hotel state
      setHotel((prev) =>
        prev ? { ...prev, description: newDescription } : null
      );

      // Refresh hotel details to ensure consistency
      fetchHotelDetails();
    } catch (error) {
      console.error("Error updating description:", error);
      throw error;
    }
  };

  return (
    <>
      <AdminPageHelmet />
      <PermissionBasedSidebarHider />
      <Toaster />
      <div className="flex flex-col gap-6 min-h-screen bg-background">
        {/* Header with navigation and language selector */}
        <div className="flex justify-between items-center">
          <IconButton onClick={() => navigate("/hotel-management/hotels")}>
            <ArrowLeft className="w-4 h-4" />
          </IconButton>
          <div className="flex items-center gap-3">
            {/* Language Selector */}
            <LanguageSelector
              selectedLanguage={selectedLanguage}
              onLanguageChange={setSelectedLanguage}
            />

            {/* Translation Loading Indicator */}
            {translationsLoading && !isBaseLanguage && (
              <div className="flex items-center gap-2 text-sm text-blue-600">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span>Loading translations...</span>
              </div>
            )}
          </div>
        </div>

        {/* Hero section with image background */}
        <div className="w-full h-80 overflow-hidden rounded-lg rounded-b-none shadow-md flex flex-col justify-between p-6 relative bg-muted">
          {/* Hero background image */}
          {heroImage && (
            <>
              <img
                src={heroImage}
                alt="Hero background"
                className="absolute inset-0 w-full h-full object-cover"
              />
              {/* Dark overlay for better text readability */}
              <div className="absolute inset-0 bg-gradient-to-b from-black/70 to-black/30 dark:from-black/80 dark:to-black/40"></div>
            </>
          )}

          {/* Fallback background when no hero image */}
          {!heroImage && (
            <div className="absolute inset-0 bg-gradient-to-br from-blue-600 to-purple-700 dark:from-blue-700 dark:to-purple-800"></div>
          )}

          {/* Content overlay */}
          <div className="hero-content h-full flex flex-col justify-between relative ">
            <div className="flex justify-between items-start">
              <div className="flex flex-col gap-2">
                <div className="flex items-center gap-2">
                  <Badge
                    color={hotel?.is_active ? "green" : "grey"}
                    className="text-xs font-medium px-3 py-1"
                  >
                    {hotel?.is_active ? "Active" : "Inactive"}
                  </Badge>
                  {hotel?.is_featured && (
                    <Badge
                      color="purple"
                      className="text-xs font-medium px-3 py-1"
                    >
                      Featured
                    </Badge>
                  )}
                </div>
                <Heading
                  level="h1"
                  className="text-4xl font-bold text-white drop-shadow-sm"
                >
                  {getTranslatedValue("name")}
                </Heading>
                <div className="flex flex-wrap items-center gap-4 mt-2">
                  {getTranslatedValue("location") && (
                    <div className="flex items-center gap-2 text-white/90 bg-black/20 dark:bg-white/20 px-3 py-1 rounded-full">
                      <MapPin className="w-4 h-4" />
                      <Text className="text-white/90">
                        {getTranslatedValue("location")}
                      </Text>
                    </div>
                  )}
                  <div className="flex items-center gap-2 text-white/90 bg-black/20 dark:bg-white/20 px-3 py-0.5 rounded-full">
                    <Text className="text-white/90 font-bold">
                      {hotel?.rating}
                    </Text>
                    <Rating
                      value={hotel?.rating || 0}
                      readOnly
                      precision={0.5}
                    />
                  </div>
                </div>
              </div>

              <div className="flex gap-x-2 ">
                {/* View Images Button - Only show if hotel has images */}
                {hotel?.images && hotel.images.length > 0 && (
                  <Button
                    variant="secondary"
                    size="small"
                    className="bg-white/90 hover:bg-white dark:bg-gray-800/90 dark:hover:bg-gray-800 dark:text-white"
                    onClick={() => setImageGalleryOpen(true)}
                  >
                    <Image className="w-4 h-4 mr-1" />
                    View Images ({hotel.images.length})
                  </Button>
                )}

                {/* Edit Button and Drawer */}
                {!rbacLoading && hasPermission("hotel_management:edit") && (
                  <Button
                    variant="secondary"
                    size="small"
                    className="bg-white/90 hover:bg-white dark:bg-gray-800/90 dark:hover:bg-gray-800 dark:text-white"
                    onClick={() => setOpen(true)}
                  >
                    <Edit className="w-4 h-4 mr-1" /> Edit
                  </Button>
                )}

                <FocusModal open={open} onOpenChange={setOpen}>
                  <FocusModal.Content className="shadow-lg flex flex-col">
                    <HotelFormModern
                      formData={{
                        name: hotel?.name ?? "",
                        handle: hotel?.handle ?? "",
                        description: hotel?.description ?? "",
                        is_active: hotel?.is_active ?? false,
                        is_featured: hotel?.is_featured ?? false,
                        is_pets_allowed: hotel?.is_pets_allowed ?? false,
                        destination_id: hotel?.destination_id ?? "",
                        website: hotel?.website ?? null,
                        email: hotel?.email ?? null,
                        phone_number: hotel?.phone_number ?? "",
                        location: hotel?.location ?? "",
                        address: hotel?.address ?? "",
                        check_in_time: hotel?.check_in_time ?? "",
                        check_out_time: hotel?.check_out_time ?? "",
                        currency: hotel?.currency ?? "",
                        rating: hotel?.rating ?? undefined,
                        id: hotel?.id,
                        media: (hotel?.images ?? []).map(
                          (image: {
                            url?: string;
                            id?: string;
                            isThumbnail?: boolean;
                          }) => ({
                            url: image.url ?? "",
                            id: image.id ?? "",
                            isThumbnail: image.isThumbnail ?? false,
                            field_id: crypto.randomUUID(),
                          })
                        ),
                        roomTypes:
                          hotel?.room_types?.map((rt: any) => rt.id) ?? [],
                        notes: hotel?.notes ?? "",
                        tags: hotel?.tags ?? [],
                        amenities: hotel?.amenities ?? [],
                        rules: hotel?.rules ?? [],
                        safety_measures: hotel?.safety_measures ?? [],
                      }}
                      roomTypes={roomTypes}
                      onSubmit={handleUpdate}
                      closeModal={() => setOpen(false)}
                      onSubmitRef={submitRef}
                    />
                  </FocusModal.Content>
                </FocusModal>
              </div>
            </div>

            <div className="flex flex-wrap gap-4 mt-auto">
              {hotel?.website && (
                <div className="bg-white/10 dark:bg-black/20 backdrop-blur-sm px-4 py-2 rounded-full flex items-center gap-2">
                  <Globe className="w-4 h-4 text-white" />
                  <Text className="text-white font-medium">
                    {hotel?.website}
                  </Text>
                </div>
              )}

              {hotel?.email && (
                <div className="bg-white/10 dark:bg-black/20 backdrop-blur-sm px-4 py-2 rounded-full flex items-center gap-2">
                  <Mail className="w-4 h-4 text-white" />
                  <Text className="text-white font-medium">{hotel?.email}</Text>
                </div>
              )}

              {hotel?.phone_number && (
                <div className="bg-white/10 dark:bg-black/20 backdrop-blur-sm px-4 py-2 rounded-full flex items-center gap-2">
                  <Phone className="w-4 h-4 text-white" />
                  <Text className="text-white font-medium">
                    {hotel?.phone_number}
                  </Text>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Room Management Buttons Section */}
        <Container className="bg-card rounded-lg rounded-t-none p-4 -mt-6 ">
          <div className="flex flex-col items-center gap-4">
            <div className="flex items-center gap-2 mb-2">
              <Bed className="w-5 h-5 text-muted-foreground" />
              <Heading
                level="h2"
                className="text-lg font-medium text-foreground"
              >
                Room Management
              </Heading>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 w-full">
              {/* <Link
                to={`/hotel-management/hotels/${hotel?.id}/rooms`}
                className="flex flex-col items-center justify-center p-4 bg-green-50 hover:bg-green-100 transition-colors rounded-lg border border-green-100 hover:shadow-md"
              >
                <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-2">
                  <Bed className="w-6 h-6 text-green-600" />
                </div>
                <Text className="font-medium text-green-700 text-center">
                  All Rooms
                </Text>
                <Text className="text-xs text-green-600/80 text-center mt-1">
                  View all hotel rooms
                </Text>
              </Link> */}
              {!rbacLoading && hasPermission("rooms:view") && (
                <Link
                  to={`/hotel-management/hotels/${hotel?.id}/room-configs`}
                  className="flex flex-col items-center justify-center p-4 bg-blue-50 hover:bg-blue-100 dark:bg-blue-950/50 dark:hover:bg-blue-950/70 transition-colors rounded-lg border border-blue-100 dark:border-blue-800 hover:shadow-md"
                >
                  <div className="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-2">
                    <CreditCard className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <Text className="font-medium text-blue-700 dark:text-blue-300 text-center">
                    Room Configurations
                  </Text>
                  <Text className="text-xs text-blue-600/80 dark:text-blue-400/80 text-center mt-1">
                    Manage room types and details
                  </Text>
                </Link>
              )}
              {!rbacLoading && hasPermission("rooms:view") && (
                <Link
                  to={`/hotel-management/hotels/${slug}/availability-new`}
                  className="flex flex-col items-center justify-center p-4 bg-purple-50 hover:bg-purple-100 dark:bg-purple-950/50 dark:hover:bg-purple-950/70 transition-colors rounded-lg border border-purple-100 dark:border-purple-800 hover:shadow-md"
                >
                  <div className="w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-2">
                    <Calendar className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                  </div>
                  <Text className="font-medium text-purple-700 dark:text-purple-300 text-center">
                    Availability
                  </Text>
                  <Text className="text-xs text-purple-600/80 dark:text-purple-400/80 text-center mt-1">
                    Manage room availability
                  </Text>
                </Link>
              )}

              {!rbacLoading && hasPermission("pricing:view") && (
                <Link
                  to={`/hotel-management/hotels/${hotel?.id}/pricing`}
                  className="flex flex-col items-center justify-center p-4 bg-amber-50 hover:bg-amber-100 dark:bg-amber-950/50 dark:hover:bg-amber-950/70 transition-colors rounded-lg border border-amber-100 dark:border-amber-800 hover:shadow-md"
                >
                  <div className="w-12 h-12 rounded-full bg-amber-100 dark:bg-amber-900 flex items-center justify-center mb-2">
                    <Tags className="w-6 h-6 text-amber-600 dark:text-amber-400" />
                  </div>
                  <Text className="font-medium text-amber-700 dark:text-amber-300 text-center">
                    Pricing
                  </Text>
                  <Text className="text-xs text-amber-600/80 dark:text-amber-400/80 text-center mt-1">
                    Set rates and special offers
                  </Text>
                </Link>
              )}

              {!rbacLoading && hasPermission("hotel_management:view") && (
                <Link
                  to={`/hotel-management/bookings?hotel_id=${hotel?.id}`}
                  className="flex flex-col items-center justify-center p-4 bg-indigo-50 hover:bg-indigo-100 dark:bg-indigo-950/50 dark:hover:bg-indigo-950/70 transition-colors rounded-lg border border-indigo-100 dark:border-indigo-800 hover:shadow-md"
                >
                  <div className="w-12 h-12 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center mb-2">
                    <Calendar className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
                  </div>
                  <Text className="font-medium text-indigo-700 dark:text-indigo-300 text-center">
                    Bookings
                  </Text>
                  <Text className="text-xs text-indigo-600/80 dark:text-indigo-400/80 text-center mt-1">
                    Manage hotel bookings
                  </Text>
                </Link>
              )}
            </div>
          </div>
        </Container>

        {/* Translation Mode Indicator */}
        {!isBaseLanguage && (
          <Container className="bg-blue-50 dark:bg-blue-950/50 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-3">
              <Globe className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              <div>
                <Text className="font-medium text-blue-800 dark:text-blue-200">
                  Translation Mode:{" "}
                  {tolgeeLanguages.find((lang) => lang.tag === selectedLanguage)
                    ?.name || selectedLanguage}
                </Text>
                <Text className="text-sm text-blue-600 dark:text-blue-300">
                  You are viewing translated content. Only translatable fields
                  (name, description, location, address, notes) are shown in the
                  selected language.
                  {translationsLoading && " Loading translations..."}
                </Text>
              </div>
            </div>
          </Container>
        )}

        {/* Tab Navigation */}
        <Suspense
          fallback={
            <div className="flex items-center justify-center p-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              <span className="ml-2 text-sm text-gray-600">
                Loading tabs...
              </span>
            </div>
          }
        >
          <HotelTabs
            activeTab={activeTab}
            onTabChange={setActiveTab}
            tabs={[
              { id: "overview", label: "Overview", icon: FileText },
              {
                id: "facilities",
                label: "Facilities & Policies",
                icon: Shield,
              },
              { id: "bailey-ai", label: "Bailey AI", icon: Sparkles },
              { id: "settings", label: "Settings", icon: Settings },
            ]}
          />
        </Suspense>

        {/* Tab Content */}
        <div className="w-full">
          <Suspense
            fallback={
              <div className="flex items-center justify-center p-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                <span className="ml-2 text-sm text-gray-600">
                  Loading tab content...
                </span>
              </div>
            }
          >
            <HotelTabContent activeTab={activeTab} tabId="overview">
              <Suspense
                fallback={
                  <div className="flex items-center justify-center p-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    <span className="ml-3 text-sm text-gray-600">
                      Loading overview...
                    </span>
                  </div>
                }
              >
                <OverviewTab
                  hotel={hotel}
                  getTranslatedValue={getTranslatedValue}
                  hasEditPermission={
                    !rbacLoading && hasPermission("hotel_management:edit")
                  }
                  onDetailsUpdate={handleDetailsUpdate}
                  onDescriptionUpdate={handleDescriptionUpdate}
                />
              </Suspense>
            </HotelTabContent>
          </Suspense>

          <Suspense
            fallback={
              <div className="flex items-center justify-center p-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                <span className="ml-2 text-sm text-gray-600">
                  Loading tab content...
                </span>
              </div>
            }
          >
            <HotelTabContent activeTab={activeTab} tabId="facilities">
              <Suspense
                fallback={
                  <div className="flex items-center justify-center p-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    <span className="ml-3 text-sm text-gray-600">
                      Loading amenities...
                    </span>
                  </div>
                }
              >
                <AmenitiesTab hotel={hotel} />
              </Suspense>
            </HotelTabContent>
          </Suspense>

          {/* <Suspense
            fallback={
              <div className="flex items-center justify-center p-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                <span className="ml-2 text-sm text-gray-600">Loading tab content...</span>
              </div>
            }
          >
            <HotelTabContent activeTab={activeTab} tabId="insights">
              <Container className="p-6">
                <HotelDashboardMetrics hotelId={hotel?.id} />
              </Container>
            </HotelTabContent>
          </Suspense> */}

          <Suspense
            fallback={
              <div className="flex items-center justify-center p-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                <span className="ml-2 text-sm text-gray-600">
                  Loading tab content...
                </span>
              </div>
            }
          >
            <HotelTabContent activeTab={activeTab} tabId="bailey-ai">
              <Suspense
                fallback={
                  <div className="flex items-center justify-center p-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    <span className="ml-3 text-sm text-gray-600">
                      Loading Bailey AI...
                    </span>
                  </div>
                }
              >
                <BaileyAITab
                  hotel={hotel}
                  hasEditPermission={
                    !rbacLoading && hasPermission("hotel_management:edit")
                  }
                />
              </Suspense>
            </HotelTabContent>
          </Suspense>

          <Suspense
            fallback={
              <div className="flex items-center justify-center p-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                <span className="ml-2 text-sm text-gray-600">
                  Loading tab content...
                </span>
              </div>
            }
          >
            <HotelTabContent activeTab={activeTab} tabId="settings">
              <Suspense
                fallback={
                  <div className="flex items-center justify-center p-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    <span className="ml-3 text-sm text-gray-600">
                      Loading settings...
                    </span>
                  </div>
                }
              >
                <HotelSettingsTab
                  hotel={hotel}
                  onDelete={handleDeleteHotel}
                  deleteOpen={deleteOpen}
                  setDeleteOpen={setDeleteOpen}
                  hasDeletePermission={
                    !rbacLoading && hasPermission("hotel_management:delete")
                  }
                  hasEditPermission={
                    !rbacLoading && hasPermission("hotel_management:edit")
                  }
                />
              </Suspense>
            </HotelTabContent>
          </Suspense>
        </div>

        {/* Enhanced Image Gallery Drawer with Lazy Loading Carousel */}
        <Drawer open={imageGalleryOpen} onOpenChange={setImageGalleryOpen}>
          <Drawer.Content className="max-w-6xl mx-auto">
            {hotel?.images && hotel.images.length > 0 ? (
              <OptimizedImageCarousel
                images={hotel.images}
                onClose={() => setImageGalleryOpen(false)}
                hotelName={hotel.name}
              />
            ) : (
              <div className="p-8">
                <div className="text-center py-12">
                  <Image className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                  <Text className="text-muted-foreground text-lg">
                    No images available
                  </Text>
                  <Text className="text-muted-foreground text-sm mt-2">
                    Images can be added when editing the hotel
                  </Text>
                </div>
              </div>
            )}
          </Drawer.Content>
        </Drawer>
      </div>
    </>
  );
};

export default HotelDetailPage;
