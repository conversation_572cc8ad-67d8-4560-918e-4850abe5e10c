import { Table, LayoutGrid } from "lucide-react";
import {
  StatusBadges,
} from "../../../components/shared/StatusFilterBadges";

import {
  Container,
  Heading,
  Text,
  Button,
  Toaster,
  Tooltip,
} from "@camped-ai/ui";
import { useMemo } from "react";
import { useNavigate, useLocation } from "react-router-dom";

import { useDestinationsManagement } from "../../../hooks/hotel-management/use-destinations-management";
import DestinationSkeleton from "../../../components/destination/destination-skeleton";
import "../../../styles/destination-modal-fix.css";
import "../../../styles/destination-badges.css";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useRbac } from "../../../hooks/use-rbac";
import { DataTableQuery } from "../../../../components/table/data-table/data-table-query";
import { DataTableRoot } from "../../../../components/table/data-table/data-table-root";
import { useReactTable, getCoreRowModel, createColumnHelper, ColumnDef } from "@tanstack/react-table";
import type { Filter } from "../../../../components/table/data-table";

// ViewToggle component for destinations
type ViewMode = "card" | "table";

interface ViewToggleProps {
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
  className?: string;
}

const ViewToggle: React.FC<ViewToggleProps> = ({
  viewMode,
  onViewModeChange,
  className = "",
}) => {
  const getViewIcon = (view: ViewMode) => {
    switch (view) {
      case "table":
        return <Table className="w-4 h-4" />;
      case "card":
        return <LayoutGrid className="w-4 h-4" />;
      default:
        return <LayoutGrid className="w-4 h-4" />;
    }
  };

  const getViewLabel = (view: ViewMode) => {
    switch (view) {
      case "table":
        return "Table View";
      case "card":
        return "Card View";
      default:
        return "Card View";
    }
  };

  const views: ViewMode[] = ["card", "table"];

  return (
    <div className={`inline-flex bg-ui-bg-subtle rounded-lg p-1 ${className}`}>
      {views.map((view) => (
        <Tooltip key={view} content={getViewLabel(view)}>
          <Button
            variant="transparent"
            size="small"
            onClick={() => onViewModeChange(view)}
            className={`px-2 py-1.5 rounded-md transition-all duration-200 ${
              viewMode === view
                ? "bg-[#165DFB] shadow-sm text-white font-medium hover:text-white hover:bg-[#336DFD]"
                : "text-ui-black hover:text-white hover:bg-[#336DFD]"
            }`}
          >
            <div className="flex items-center gap-2">
              {getViewIcon(view)}
            </div>
          </Button>
        </Tooltip>
      ))}
    </div>
  );
};

const PageClient = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { hasPermission } = useRbac();
  const { t } = useTranslation();

  // Use the new destinations management hook
  const {
    destinations,
    isLoading,
    isFetching,
    filters,
    pagination,
  } = useDestinationsManagement();

  // Get current view mode from URL
  const searchParams = new URLSearchParams(location.search);
  const currentView = (searchParams.get("view") as ViewMode) || "card";

  // Define table columns
  const columnHelper = createColumnHelper<any>();

  const columns = useMemo<ColumnDef<any, any>[]>(() => [
    columnHelper.accessor("name", {
      header: "Destination",
      cell: ({ row }) => {
        const destination = row.original;
        return (
          <div className="flex items-center">
            <div className="flex-shrink-0 h-10 w-10 relative">
              {destination.thumbnailUrl ? (
                <img
                  className="h-10 w-10 rounded-md object-cover"
                  src={destination.thumbnailUrl}
                  alt=""
                />
              ) : (
                <div className="h-10 w-10 rounded-md bg-gradient-to-r from-blue-500 to-purple-500"></div>
              )}
            </div>
            <div className="ml-4">
              <div className="text-sm font-medium text-foreground">
                {destination.name}
              </div>
              <div className="text-sm text-muted-foreground truncate max-w-xs">
                {destination.description || "No description"}
              </div>
            </div>
          </div>
        );
      },
    }),
    columnHelper.accessor("country", {
      header: "Country",
      cell: ({ getValue }) => (
        <div className="text-sm text-foreground">
          {getValue() || "--"}
        </div>
      ),
    }),
    columnHelper.accessor("hotelCount", {
      header: "Hotels",
      cell: ({ getValue }) => (
        <div className="text-sm text-foreground font-medium">
          {getValue() || 0}
        </div>
      ),
    }),
    columnHelper.accessor("is_active", {
      header: "Status",
      cell: ({ row }) => {
        const destination = row.original;
        return (
          <StatusBadges
            isActive={destination.is_active}
            isFeatured={destination.is_featured}
            size="small"
          />
        );
      },
    }),
    columnHelper.display({
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const destination = row.original;
        return (
          <div className="flex gap-2">
            <Button
              variant="secondary"
              size="small"
              className="bg-background border border-border shadow-sm hover:bg-accent flex items-center gap-1 px-3 py-1.5 rounded-md transition-all text-foreground text-xs"
              onClick={(e) => {
                e.stopPropagation();
                navigate(
                  `/hotel-management/destinations/${destination.handle}`
                );
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="w-3.5 h-3.5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                <path
                  fillRule="evenodd"
                  d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                  clipRule="evenodd"
                />
              </svg>
              <span>View</span>
            </Button>
            <Button
              variant="secondary"
              size="small"
              className="bg-background border border-border shadow-sm hover:bg-accent flex items-center gap-1 px-3 py-1.5 rounded-md transition-all text-foreground text-xs"
              onClick={(e) => {
                e.stopPropagation();
                navigate(
                  `/hotel-management/hotels?destination=${destination.id}`
                );
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="w-3.5 h-3.5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
              </svg>
              <span>Hotels</span>
            </Button>
          </div>
        );
      },
    }),
  ], [navigate]);

  // Define filters for DataTableQuery
  const tableFilters = useMemo<Filter[]>(() => [
    {
      key: "status",
      label: "Status",
      type: "select",
      options: [
        { label: "All", value: "" },
        { label: "Active", value: "active" },
        { label: "Inactive", value: "inactive" },
        { label: "Featured", value: "featured" },
      ],
    },
  ], []);

  // Create table instance
  const table = useReactTable({
    data: destinations,
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    manualFiltering: true,
    manualSorting: true,
    pageCount: Math.ceil(pagination.totalCount / pagination.pageSize),
    state: {
      pagination: {
        pageIndex: pagination.currentPage - 1,
        pageSize: pagination.pageSize,
      },
    },
  });

  // Get a gradient color based on index
  const getGradient = (index: number) => {
    const gradients = [
      "from-blue-500 to-purple-500",
      "from-green-500 to-teal-500",
      "from-yellow-500 to-orange-500",
      "from-pink-500 to-rose-500",
      "from-indigo-500 to-blue-500",
      "from-red-500 to-pink-500",
    ];
    return gradients[index % gradients.length];
  };

  const path = "/hotel-management/destinations";
  const hasCreate = hasPermission("destinations:create");
  const hasImport = hasPermission("destinations:bulk_import");
  const hasExport = hasPermission("destinations:export");

  return (
    <>
      <Container className="divide-y p-0">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-3">
          <div>
            <Heading level="h2">Destination</Heading>
          </div>
          <div className="flex items-center gap-x-2">
            <ViewToggle
              viewMode={currentView}
              onViewModeChange={(view) => {
                const newSearchParams = new URLSearchParams(searchParams);
                newSearchParams.set("view", view);
                navigate(`?${newSearchParams.toString()}`);
              }}
            />
            {hasExport && (
              <Button
                variant="secondary"
                size="small"
                onClick={() => navigate(`${path}/export`)}
                className="flex items-center gap-2"
              >
                Export
              </Button>
            )}
            {hasImport && (
              <Button
                variant="secondary"
                size="small"
                onClick={() => navigate(`${path}/import`)}
                className="flex items-center gap-2"
              >
                Import
              </Button>
            )}
            {hasCreate && (
              <Button size="small" asChild>
                <Link to={`${path}/create`}>{t("actions.create")}</Link>
              </Button>
            )}
          </div>
        </div>

        {/* DataTable Query Controls - shown for all view modes */}
        <DataTableQuery
          search="autofocus"
          filters={tableFilters}
        />

        {/* Content based on view mode */}
        {currentView === "table" && (
          <DataTableRoot
            table={table}
            columns={columns}
            count={pagination.totalCount}
            pagination
            navigateTo={(row) =>
              `/hotel-management/destinations/${row.original.handle}`
            }
          />
        )}

        {currentView === "card" && (
          <>
            {isLoading || isFetching ? (
              <DestinationSkeleton
                viewMode="grid"
                count={6}
              />
            ) : destinations.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                {destinations.map((destination, index) => (
                  <div
                    key={destination.id}
                    className="border border-border rounded-lg overflow-hidden bg-card shadow-sm hover:shadow-md transition-all cursor-pointer flex flex-col"
                    onClick={() =>
                      navigate(
                        `/hotel-management/destinations/${destination.handle}`
                      )
                    }
                  >
                    <div className="h-40 relative flex-shrink-0">
                      {destination.thumbnailUrl ? (
                        <>
                          <img
                            src={destination.thumbnailUrl}
                            alt={destination.name}
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                            <Heading
                              level="h3"
                              className="text-white text-2xl font-bold"
                            >
                              {destination.name}
                            </Heading>
                          </div>
                        </>
                      ) : (
                        <div
                          className={`h-full w-full bg-gradient-to-r ${getGradient(
                            index
                          )} flex items-center justify-center`}
                        >
                          <Heading
                            level="h3"
                            className="text-white text-2xl font-bold"
                          >
                            {destination.name}
                          </Heading>
                        </div>
                      )}
                      {/* Hotel count badge */}
                      <div className="absolute top-2 right-2 bg-white/45 rounded-full px-2 py-1 text-xs font-medium flex items-center gap-1 shadow-sm">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="w-3.5 h-3.5 text-blue-600"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
                        </svg>
                        <span className="text-foreground">
                          {destination.hotelCount || 0} Hotels
                        </span>
                      </div>
                    </div>
                    <div className="p-4 flex flex-col flex-grow">
                      <div className="flex-grow flex flex-col">
                        <div className="flex justify-between items-center mb-2">
                          <Text className="font-medium">
                            {destination.name} - {destination.country}
                          </Text>
                          <StatusBadges
                            isActive={destination.is_active}
                            isFeatured={destination.is_featured}
                            size="small"
                          />
                        </div>
                        <Text className="text-sm text-muted-foreground line-clamp-2 mb-3">
                          {destination.description || "No description available"}
                        </Text>
                      </div>

                      {/* Action buttons */}
                      <div className="flex gap-2 mt-auto">
                        <Button
                          variant="secondary"
                          size="small"
                          className="bg-background border border-border shadow-sm hover:bg-accent flex items-center gap-1 px-3 py-1.5 rounded-md transition-all text-foreground text-xs flex-1"
                          onClick={(e) => {
                            e.stopPropagation();
                            navigate(
                              `/hotel-management/destinations/${destination.handle}`
                            );
                          }}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="w-3.5 h-3.5"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                            <path
                              fillRule="evenodd"
                              d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                          <span>View</span>
                        </Button>
                        <Button
                          variant="secondary"
                          size="small"
                          className="bg-background border border-border shadow-sm hover:bg-accent flex items-center gap-1 px-3 py-1.5 rounded-md transition-all text-foreground text-xs flex-1"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Navigate to hotels filtered by this destination
                            navigate(
                              `/hotel-management/hotels?destination=${destination.id}`
                            );
                          }}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="w-3.5 h-3.5"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
                          </svg>
                          <span>Hotels</span>
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 bg-muted/50 rounded-lg">
                <Text className="text-muted-foreground">
                  {filters.search ||
                  filters.is_featured !== null ||
                  filters.is_active !== null
                    ? "No destinations match your search criteria"
                    : "No destinations found"}
                </Text>
              </div>
            )}
          </>
        )}
      </Container>
      <Toaster />
    </>
  );
};

export default PageClient;
