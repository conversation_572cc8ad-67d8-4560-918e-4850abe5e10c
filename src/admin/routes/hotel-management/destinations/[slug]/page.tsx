import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  FocusModal,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Drawer,
} from "@camped-ai/ui";
import { useEffect, useState, useRef, Suspense, lazy } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { DestinationData, HotelData } from "../../../../types";
import DestinationFormModern, { DestinationFormData } from "../../../../components/destination-form-modern";
import { toast, Toaster } from "@camped-ai/ui";
import "../../../../styles/theme-variables.css";
import "../../../../styles/destination-modal-fix.css";
import "./description-styles.css";

import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { MediaField } from "../../../../components/hotel/media-item";
import LanguageSelector from "../../../../components/language-selector";
import { useDestinationTranslations } from "../../../../hooks/translations/useDestinationTranslations";
import { useProjectLanguages } from "../../../../hooks/languages/useProjectLanguages";
import OptimizedImageCarousel from "../../../../components/hotel/optimized-image-carousel";
import { useRbac } from "../../../../hooks/use-rbac";
import {
  Globe,
  Image,
  Hotel,
  FileText,
  ArrowLeft,
  BarChart3,
  HelpCircle,
  Settings,
  Sparkles,
} from "lucide-react";
import AdminPageHelmet from "../../../../components/AdminPageHelmet";

// New tab-based components
import DestinationTabs from "../../../../components/destination/destination-tabs";
import DestinationTabContent from "../../../../components/destination/destination-tab-content";
import DestinationBanner from "../../../../components/destination/destination-banner";
import OverviewTab from "../../../../components/destination/tabs/overview-tab";
import HotelsTab from "../../../../components/destination/tabs/hotels-tab";
import FAQsTab from "../../../../components/destination/tabs/faqs-tab";
import InsightsTab from "../../../../components/destination/tabs/insights-tab";

import SettingsTab from "../../../../components/destination/tabs/settings-tab";
import { useTabState } from "../../../../hooks/useTabState";

// Dynamically import BaileyAITab for better performance
const BaileyAITab = lazy(() => import("./_components/bailey-ai-tab"));

const DestinationDetailPage = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const [destination, setDestination] = useState<DestinationData | null>(null);
  const [destinationImages, setDestinationImages] = useState<any[]>([]);
  const [hotels, setHotels] = useState<HotelData[]>([]);
  const [isLoadingHotels, setIsLoadingHotels] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredHotels, setFilteredHotels] = useState<HotelData[]>([]);
  const [formData, setFormData] = useState<DestinationFormData>({
    name: "",
    handle: "",
    description: "",
    is_active: true,
    is_featured: false,
    country: "",
    location: null,
    tags: null,
    ai_content: null,
    media: [],
  });
  const [isLoading, setIsLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const [dataLoaded, setDataLoaded] = useState(false);
  const [deleteOpen, setDeleteOpen] = useState(false);
  const [imageGalleryOpen, setImageGalleryOpen] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);

  // Tab state management
  const { activeTab, setActiveTab } = useTabState({ defaultTab: "overview" });

  // Language selector state
  const [selectedLanguage, setSelectedLanguage] = useState("en");

  // Translation hooks
  const { languages: tolgeeLanguages } = useProjectLanguages();
  const {
    translations,
    loadTranslations,
    arrayTranslations,
    nestedObjectTranslations,
  } = useDestinationTranslations(destination?.id || "new");

  // Determine if current language is base language
  const isBaseLanguage =
    selectedLanguage === "en" ||
    tolgeeLanguages.find((lang) => lang.tag === selectedLanguage)?.base ===
    true;

  // Track loaded translations to prevent infinite loops
  const loadedTranslationsRef = useRef<Set<string>>(new Set());

  // State for the page

  // Load translations when language changes - FIXED TO PREVENT INFINITE LOOPS
  useEffect(() => {
    const loadTranslationsForLanguage = async () => {
      if (!isBaseLanguage && destination?.id && destination.id !== "new") {
        const translationKey = `${selectedLanguage}-${destination.id}`;

        // Check if we've already loaded translations for this language/destination combo
        if (loadedTranslationsRef.current.has(translationKey)) {
          console.log(
            `Translations already loaded for ${translationKey}, skipping...`
          );
          return;
        }

        try {
          console.log(
            `Loading translations for language: ${selectedLanguage}, destination: ${destination.id}`
          );
          await loadTranslations(selectedLanguage);
          loadedTranslationsRef.current.add(translationKey);
          console.log(`✅ Translations loaded for ${selectedLanguage}`);
        } catch (error) {
          console.error(
            `❌ Error loading translations for ${selectedLanguage}:`,
            error
          );
        }
      }
    };

    loadTranslationsForLanguage();
  }, [selectedLanguage, isBaseLanguage, destination?.id]);

  // Helper function to get translated content
  const getTranslatedContent = (
    fieldName: string,
    baseValue: string
  ): string => {
    if (isBaseLanguage || !destination?.id) {
      return baseValue;
    }

    const translationKey = `destination.${destination.id}.${fieldName}`;
    const translatedValue = translations[selectedLanguage]?.[translationKey];

    return translatedValue || baseValue;
  };

  // Helper function to get translated tags
  const getTranslatedTags = (): string[] => {
    console.log(
      `🏷️ getTranslatedTags called for language: ${selectedLanguage}`
    );
    console.log(`🏠 isBaseLanguage: ${isBaseLanguage}`);
    console.log(`📊 arrayTranslations state:`, arrayTranslations);
    console.log(`🎯 destination?.id:`, destination?.id);

    if (isBaseLanguage || !destination?.id) {
      console.log(`🏷️ Using base language tags`);
      // Handle different formats of tags for base language or fallback
      let tagsArray: string[] = [];
      if (destination?.tags) {
        if (Array.isArray(destination.tags)) {
          tagsArray = destination.tags;
        } else if (typeof destination.tags === "string") {
          try {
            const parsed = JSON.parse(destination.tags as string);
            tagsArray = Array.isArray(parsed) ? parsed : [];
          } catch (e) {
            tagsArray = (destination.tags as string)
              .split(",")
              .map((t: string) => t.trim());
          }
        } else if (
          typeof destination.tags === "object" &&
          destination.tags !== null
        ) {
          tagsArray = Object.values(destination.tags);
        }
      }
      console.log(`🏷️ Base language tags result:`, tagsArray);
      return tagsArray;
    }

    // For non-base languages, get translated tags from arrayTranslations
    console.log(
      `🔍 Looking for translated tags in arrayTranslations[${selectedLanguage}]?.tags`
    );
    const translatedTags = arrayTranslations[selectedLanguage]?.tags;
    console.log(`📥 Raw translated tags result:`, translatedTags);

    if (
      translatedTags &&
      Array.isArray(translatedTags) &&
      translatedTags.length > 0
    ) {
      console.log(
        `🏷️ Found translated tags for ${selectedLanguage}:`,
        translatedTags
      );
      return translatedTags;
    }

    console.log(
      `🏷️ No translated tags found for ${selectedLanguage}, falling back to base language`
    );

    // Fallback to base language tags if no translation available
    let fallbackTagsArray: string[] = [];
    if (destination?.tags) {
      if (Array.isArray(destination.tags)) {
        fallbackTagsArray = destination.tags;
      } else if (typeof destination.tags === "string") {
        try {
          const parsed = JSON.parse(destination.tags as string);
          fallbackTagsArray = Array.isArray(parsed) ? parsed : [];
        } catch (e) {
          fallbackTagsArray = (destination.tags as string)
            .split(",")
            .map((t: string) => t.trim());
        }
      } else if (
        typeof destination.tags === "object" &&
        destination.tags !== null
      ) {
        fallbackTagsArray = Object.values(destination.tags);
      }
    }
    console.log(`🏷️ Fallback tags result:`, fallbackTagsArray);
    return fallbackTagsArray;
  };

  // Helper function to get translated FAQs
  const getTranslatedFaqs = () => {
    if (isBaseLanguage || !destination?.faqs) {
      return destination?.faqs || [];
    }

    // For non-base languages, try to get translated FAQs
    const translatedFaqs = nestedObjectTranslations[selectedLanguage]?.faqs;
    return translatedFaqs || destination?.faqs || [];
  };

  // Filter hotels based on search query
  useEffect(() => {
    if (!hotels) return;

    if (searchQuery.trim() === "") {
      setFilteredHotels(hotels);
    } else {
      const filtered = hotels.filter((hotel) =>
        hotel.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredHotels(filtered);
    }
  }, [searchQuery, hotels]);

  useEffect(() => {
    if (!slug || dataLoaded) return;

    const loadData = async () => {
      try {
        // Fetch destination data
        const response = await fetch(
          `/admin/hotel-management/destinations/${slug}`,
          {
            credentials: "include",
          }
        );
        const data = await response.json();

        const destinationData = Array.isArray(data?.destination)
          ? data?.destination[0]
          : data.destination;

        if (destinationData) {
          setDestination(destinationData);

          // Fetch destination images
          if (destinationData.id) {
            try {
              const imagesResponse = await fetch(
                `/admin/hotel-management/destinations/${destinationData.id}/images`,
                { credentials: "include" }
              );
              const imagesData = await imagesResponse.json();

              if (imagesData.images) {
                setDestinationImages(imagesData.images);

                // Convert images to media format for the form
                const mediaItems: MediaField[] = imagesData.images.map(
                  (img: any) => ({
                    id: img.id,
                    url: img.url,
                    isThumbnail: img.isThumbnail || false,
                    field_id: img.id,
                  })
                );

                setFormData({
                  id: destinationData.id,
                  name: destinationData.name || "",
                  handle: destinationData.handle || "",
                  description: destinationData.description || "",
                  is_active: destinationData.is_active ?? true,
                  is_featured: destinationData.is_featured ?? false,
                  country: destinationData.country || "",
                  location: destinationData.location || null,
                  tags: destinationData.tags || null,
                  website: destinationData.website || null,
                  ai_content: destinationData.ai_content || null,
                  faqs: destinationData.faqs || [],
                  media: mediaItems,
                });
              }
            } catch (imageError) {
              console.error("Error fetching destination images:", imageError);
              // Still set form data even if images fail to load
              setFormData({
                id: destinationData.id,
                name: destinationData.name || "",
                handle: destinationData.handle || "",
                description: destinationData.description || "",
                is_active: destinationData.is_active ?? true,
                is_featured: destinationData.is_featured ?? false,
                country: destinationData.country || "",
                location: destinationData.location || null,
                tags: destinationData.tags || null,
                website: destinationData.website || null,
                ai_content: destinationData.ai_content || null,
                faqs: destinationData.faqs || [],
                media: [],
              });
            }

            // Fetch hotels for this destination
            setIsLoadingHotels(true);
            try {
              const hotelsResponse = await fetch(
                `/admin/hotel-management/hotels?limit=100`,
                {
                  credentials: "include",
                }
              );

              if (hotelsResponse.ok) {
                const hotelsData = await hotelsResponse.json();

                // Filter hotels by destination_id
                const destinationHotels = hotelsData.hotels.filter(
                  (hotel: HotelData) =>
                    hotel.destination_id === destinationData.id
                );

                // Fetch images for each hotel
                const hotelsWithImages = await Promise.all(
                  destinationHotels.map(async (hotel: HotelData) => {
                    try {
                      const imagesResponse = await fetch(
                        `/admin/hotel-management/hotels/${hotel.id}/images`,
                        { credentials: "include" }
                      );

                      if (imagesResponse.ok) {
                        const imagesData = await imagesResponse.json();

                        if (imagesData.images && imagesData.images.length > 0) {
                          return {
                            ...hotel,
                            images: imagesData.images,
                          };
                        }
                      }

                      return hotel;
                    } catch (error) {
                      console.error(
                        `Error fetching images for hotel ${hotel.id}:`,
                        error
                      );
                      return hotel;
                    }
                  })
                );

                setHotels(hotelsWithImages);
                setFilteredHotels(hotelsWithImages);
              }
            } catch (error) {
              console.error("Error fetching hotels:", error);
              toast.error("Error", {
                description: "Failed to load hotels for this destination",
              });
            } finally {
              setIsLoadingHotels(false);
            }
          }

          // Mark data as loaded
          setDataLoaded(true);
        } else {
          // Handle case when destination data is not found
          console.error("Destination not found for slug:", slug);
          toast.error("Error", {
            description: "Destination not found",
          });
        }
      } catch (error) {
        console.error("Error fetching destination:", error);
        toast.error("Error", {
          description: "Failed to load destination",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [slug, dataLoaded]); // Depend on slug and dataLoaded

  // Reset dataLoaded when slug changes
  useEffect(() => {
    setDataLoaded(false);
  }, [slug]);

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-background">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!destination) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="py-8">
          <div className="text-center py-12 bg-muted rounded-lg">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-12 w-12 text-muted-foreground mx-auto mb-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <Text className="text-muted-foreground mb-4">
              Destination not found
            </Text>
            <Button
              variant="primary"
              size="small"
              onClick={() => navigate("/hotel-management/destinations")}
            >
              Back to Destinations
            </Button>
          </div>
        </Container>
      </>
    );
  }

  const handleUpdate = async (updatedData?: DestinationFormData) => {
    // Use the updated data if provided, otherwise use the current formData
    const dataToUse = updatedData || formData;
    try {
      // First update the destination basic info
      // Ensure tags is properly formatted
      let formattedTags = dataToUse.tags;
      console.log("Data in handle update", dataToUse);
      if (typeof dataToUse.tags === "string") {
        try {
          // Try to parse if it's a JSON string
          formattedTags = JSON.parse(dataToUse.tags as string);
        } catch (e) {
          // If not a valid JSON, split by comma
          formattedTags = (dataToUse.tags as string)
            .split(",")
            .map((tag) => tag.trim());
        }
      }

      const destinationData = {
        name: dataToUse.name,
        handle: dataToUse.handle,
        description: dataToUse.description,
        is_active: dataToUse.is_active,
        is_featured: dataToUse.is_featured,
        country: dataToUse.country,
        location: dataToUse.location,
        tags: formattedTags,
        website: dataToUse.website,
        ai_content: dataToUse.ai_content,
        faqs: dataToUse.faqs || [],
        id: destination?.id,
      };

      const response = await fetch(`/admin/hotel-management/destinations`, {
        method: "PUT",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(destinationData),
      });

      const data = await response.json();

      if (response.ok) {
        // Handle image uploads for new images
        if (dataToUse.media && dataToUse.media.length > 0) {
          const destinationId = destination?.id;

          // Upload each new image (ones with file property)
          for (const media of dataToUse.media) {
            if (media.file) {
              const formData = new FormData();
              formData.append("files", media.file);

              // Add metadata including thumbnail flag
              const metadata = {
                isThumbnail: media.isThumbnail,
              };
              formData.append("metadata", JSON.stringify(metadata));

              try {
                const uploadResponse = await fetch(
                  `/admin/hotel-management/destinations/${destinationId}/upload`,
                  {
                    method: "POST",
                    credentials: "include",
                    body: formData,
                  }
                );

                if (!uploadResponse.ok) {
                  console.error(
                    "Failed to upload image",
                    await uploadResponse.text()
                  );
                }
              } catch (uploadError) {
                console.error("Error uploading image:", uploadError);
              }
            }
          }
        }

        toast.success("Success", {
          description: "Destination updated successfully",
        });

        // Refresh the destination data
        const refreshResponse = await fetch(
          `/admin/hotel-management/destinations/${slug}`,
          {
            credentials: "include",
          }
        );
        const refreshData = await refreshResponse.json();
        const destinationData = Array.isArray(refreshData?.destination)
          ? refreshData?.destination[0]
          : refreshData.destination;

        if (destinationData) {
          setDestination(destinationData);

          // Refresh images
          const imagesResponse = await fetch(
            `/admin/hotel-management/destinations/${destinationData.id}/images`,
            { credentials: "include" }
          );
          const imagesData = await imagesResponse.json();

          if (imagesData.images) {
            setDestinationImages(imagesData.images);
          }
        }

        return true;
      } else {
        toast.error("Error", {
          description: data.message || "Failed to update destination",
        });
        return false;
      }
    } catch (error) {
      console.error("Error updating destination:", error);
      toast.error("Error", {
        description: "An unexpected error occurred",
      });
      return false;
    }
  };

  const handleDelete = async () => {
    try {
      const response = await fetch(`/admin/hotel-management/destinations`, {
        method: "DELETE",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ids: destination?.id,
        }),
      });

      if (response.ok) {
        toast.success("Success", {
          description: "Destination deleted successfully",
        });
        navigate("/hotel-management/destinations");
      } else {
        const data = await response.json();
        toast.error("Error", {
          description: data.message || "Failed to delete destination",
        });
      }
    } catch (error) {
      toast.error("Error", {
        description: "An unexpected error occurred",
      });
    }
  };

  // Handle Bailey AI sync
  const handleSync = async () => {
    if (!destination?.id) {
      console.error("Cannot sync with Bailey AI: destination ID is missing");
      return;
    }

    setIsSyncing(true);

    try {
      console.log("🤖 Syncing with Bailey AI for destination:", destination.id);

      // Use the dedicated Bailey AI sync endpoint
      const response = await fetch(`/admin/hotel-management/destinations/${destination.id}/bailey-ai-sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        console.log("✅ Bailey AI sync successful:", result);

        toast.success("Success", {
          description: "Successfully synced with Bailey AI",
        });

        // Update destination data if the API returns updated content
        if (result.destination?.ai_content) {
          // Update the destination state with the new AI content
          setDestination(prev => prev ? { ...prev, ai_content: result.destination.ai_content } : null);
        }
      } else {
        const errorData = await response.json().catch(() => ({}));
        console.error("❌ Bailey AI sync failed:", response.status, errorData);

        // Check if it's a partial success (database updated but external API failed)
        if (response.status === 207) {
          toast.error("Partial Success", {
            description: "Destination updated in database, but Bailey AI sync failed. Please try again.",
          });

          // Update destination data if available
          if (errorData.destination?.ai_content) {
            setDestination(prev => prev ? { ...prev, ai_content: errorData.destination.ai_content } : null);
          }
        } else {
          toast.error("Error", {
            description: errorData.message || "Failed to sync with Bailey AI. Please try again.",
          });
        }
      }
    } catch (error) {
      console.error("Error syncing with Bailey AI:", error);

      toast.error("Error", {
        description: "Network error while syncing with Bailey AI.",
      });
    } finally {
      setIsSyncing(false);
    }
  };

  const handleDescriptionUpdate = async (newDescription: string) => {
    if (!destination) return;

    try {
      const response = await fetch(`/admin/hotel-management/destinations`, {
        method: "PUT",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id: destination.id,
          description: newDescription,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || "Failed to update description");
      }

      // Update the local destination state
      setDestination(prev => prev ? { ...prev, description: newDescription } : null);

      // Also update the form data to keep it in sync
      setFormData(prev => ({ ...prev, description: newDescription }));
    } catch (error) {
      console.error("Error updating description:", error);
      throw error;
    }
  };

  const handleDetailsUpdate = async (field: string, value: string | string[]) => {
    if (!destination) return;

    try {
      const updateData: any = {
        id: destination.id,
        [field]: value,
      };

      const response = await fetch(`/admin/hotel-management/destinations`, {
        method: "PUT",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to update ${field}`);
      }

      // Update the local destination state
      setDestination(prev => prev ? { ...prev, [field]: value } : null);

      // Also update the form data to keep it in sync
      setFormData(prev => ({ ...prev, [field]: value }));
    } catch (error) {
      console.error(`Error updating ${field}:`, error);
      throw error;
    }
  };

  // Filter for active hotels
  const activeHotels = hotels.filter((hotel) => hotel.is_active);

  // Find a hero image for the destination
  const heroImage =
    destinationImages.length > 0
      ? destinationImages.find((img) => img.isThumbnail)?.url ||
      destinationImages[0].url
      : null;

  console.log("destination", destination);
  return (
    <>
    <AdminPageHelmet />
      <PermissionBasedSidebarHider />
      <Toaster />
      <div className="flex flex-col gap-6 min-h-screen bg-background">
        {/* Header with navigation and language selector */}
        <div className="flex justify-between items-center">
          <IconButton
            onClick={() => navigate("/hotel-management/destinations")}
          >
            <ArrowLeft className="w-4 h-4" />
          </IconButton>
          <div className="flex items-center gap-3">
            {/* Language Selector */}
            <LanguageSelector
              selectedLanguage={selectedLanguage}
              onLanguageChange={setSelectedLanguage}
            />

            {/* Translation Loading Indicator */}
            {!isBaseLanguage && (
              <div className="flex items-center gap-2 text-sm text-blue-600">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span>Loading translations...</span>
              </div>
            )}
          </div>
        </div>

        {/* Banner Section */}
        <DestinationBanner
          destination={destination}
          destinationImages={destinationImages}
          activeHotelsCount={activeHotels.length}
          heroImage={heroImage}
          onEdit={() => setOpen(true)}
          onDelete={handleDelete}
          onViewImages={() => setImageGalleryOpen(true)}
          onSync={handleSync}
          deleteOpen={deleteOpen}
          setDeleteOpen={setDeleteOpen}
          hasEditPermission={hasPermission("destinations:edit")}
          hasDeletePermission={hasPermission("destinations:delete")}
          isSyncing={isSyncing}
          getTranslatedContent={getTranslatedContent}
        />

        {/* Tab Navigation */}
        <DestinationTabs
          activeTab={activeTab}
          onTabChange={setActiveTab}
          tabs={[
            { id: "overview", label: "Overview", icon: FileText },
            { id: "hotels", label: "Hotels", icon: Hotel, count: hotels.length },
            { id: "faqs", label: "FAQs", icon: HelpCircle },
            { id: "bailey-ai", label: "Bailey AI", icon: Sparkles },
            { id: "settings", label: "Settings", icon: Settings },
          ]}
        />

        {/* Translation Mode Indicator */}
        {!isBaseLanguage && (
          <Container className="bg-blue-50 dark:bg-blue-950/50 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-3">
              <Globe className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              <div>
                <Text className="font-medium text-blue-800 dark:text-blue-200">
                  Translation Mode:{" "}
                  {tolgeeLanguages.find((lang) => lang.tag === selectedLanguage)
                    ?.name || selectedLanguage}
                </Text>
                <Text className="text-sm text-blue-600 dark:text-blue-300">
                  You are viewing translated content. Only translatable fields
                  (name, description, location, tags) are shown in the selected
                  language.
                </Text>
              </div>
            </div>
          </Container>
        )}

        {/* Tab Content */}
        <div className="w-full">
          <DestinationTabContent activeTab={activeTab} tabId="overview">
            <OverviewTab
              destination={destination}
              getTranslatedContent={getTranslatedContent}
              getTranslatedTags={getTranslatedTags}
              hasEditPermission={hasPermission("destinations:edit")}
              onDescriptionUpdate={handleDescriptionUpdate}
              onDetailsUpdate={handleDetailsUpdate}
            />
          </DestinationTabContent>

          <DestinationTabContent activeTab={activeTab} tabId="hotels">
            <HotelsTab
              hotels={hotels}
              filteredHotels={filteredHotels}
              isLoadingHotels={isLoadingHotels}
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              destination={destination}
              hasCreatePermission={hasPermission("hotel_management:create")}
              hasEditPermission={hasPermission("hotel_management:edit")}
              getTranslatedContent={getTranslatedContent}
            />
          </DestinationTabContent>

          <DestinationTabContent activeTab={activeTab} tabId="faqs">
            <FAQsTab
              faqs={destination?.faqs || []}
              getTranslatedFaqs={getTranslatedFaqs}
              hasEditPermission={hasPermission("destinations:edit")}
            />
          </DestinationTabContent>

          <DestinationTabContent activeTab={activeTab} tabId="insights">
            <InsightsTab
              destination={destination}
              hotels={hotels}
              activeHotelsCount={activeHotels.length}
            />
          </DestinationTabContent>

          <DestinationTabContent activeTab={activeTab} tabId="bailey-ai">
            <Suspense
              fallback={
                <div className="flex items-center justify-center p-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  <span className="ml-3 text-sm text-gray-600">Loading Bailey AI...</span>
                </div>
              }
            >
              <BaileyAITab
                destination={destination}
                hasEditPermission={hasPermission("destinations:edit")}
              />
            </Suspense>
          </DestinationTabContent>

          <DestinationTabContent activeTab={activeTab} tabId="settings">
            <SettingsTab
              destination={destination}
              onDelete={handleDelete}
              deleteOpen={deleteOpen}
              setDeleteOpen={setDeleteOpen}
              hasDeletePermission={hasPermission("destinations:delete")}
              hasEditPermission={hasPermission("destinations:edit")}
            />
          </DestinationTabContent>
        </div>
      </div>

      {/* Enhanced Image Gallery Drawer with Lazy Loading Carousel */}
      <Drawer open={imageGalleryOpen} onOpenChange={setImageGalleryOpen}>
        <Drawer.Content className="max-w-6xl mx-auto">
          {destinationImages && destinationImages.length > 0 ? (
            <OptimizedImageCarousel
              images={destinationImages}
              onClose={() => setImageGalleryOpen(false)}
              hotelName={getTranslatedContent("name", destination?.name || "")}
            />
          ) : (
            <div className="p-8">
              <div className="text-center py-12">
                <Image className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <Text className="text-muted-foreground text-lg">
                  No images available
                </Text>
                <Text className="text-muted-foreground text-sm mt-2">
                  Images can be added when editing the destination
                </Text>
              </div>
            </div>
          )}
        </Drawer.Content>
      </Drawer>

      {/* Edit Destination Drawer */}
      <FocusModal open={open} onOpenChange={setOpen}>
        <FocusModal.Content className="destination-modal">
          <DestinationFormModern
            destinationId={destination?.id}
            formData={formData as any}
            setFormData={setFormData}
            onSubmit={handleUpdate}
            closeModal={() => setOpen(false)}
            isEdit
          />
        </FocusModal.Content>
      </FocusModal>
    </>
  );
};

export default DestinationDetailPage;
