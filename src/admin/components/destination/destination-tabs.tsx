import React from "react";
import { clx } from "@camped-ai/ui";
import {
  FileText,
  Hotel,
  HelpCircle,
  BarChart3,
  Settings,
  Sparkles
} from "lucide-react";
import "../../styles/modern-tabs.css";
import "../../styles/global-modal-zindex.css";

export type TabId = "overview" | "hotels" | "faqs" | "insights" | "bailey-ai" | "settings";

export interface Tab {
  id: TabId;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  count?: number;
}

interface DestinationTabsProps {
  activeTab: TabId;
  onTabChange: (tabId: TabId) => void;
  tabs?: Tab[];
  className?: string;
  sticky?: boolean;
}

const defaultTabs: Tab[] = [
  {
    id: "overview",
    label: "Overview",
    icon: FileText,
  },
  {
    id: "hotels",
    label: "Hotels",
    icon: Hotel,
  },
  {
    id: "faqs",
    label: "FAQs",
    icon: HelpCircle,
  },
  {
    id: "bailey-ai",
    label: "Bailey AI",
    icon: Sparkles,
  },
  {
    id: "settings",
    label: "Settings",
    icon: Settings,
  },
];

const DestinationTabs: React.FC<DestinationTabsProps> = ({
  activeTab,
  onTabChange,
  tabs = defaultTabs,
  className,
  sticky = true,
}) => {
  return (
    <div
      className={clx(
        "destination-tabs-container w-full bg-gradient-to-b from-gray-50/90 to-white/95 border-b border-gray-200/80 backdrop-blur-md",
        sticky && "sticky top-0 z-40 shadow-xl shadow-gray-200/50",
        className
      )}
    >
      <div className="max-w-full overflow-x-auto scrollbar-hide px-4">
        <nav className="destination-tabs-nav flex space-x-1 min-w-max md:min-w-0 pt-3 pb-1" role="tablist">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;

            return (
              <button
                key={tab.id}
                role="tab"
                aria-selected={isActive}
                aria-controls={`${tab.id}-panel`}
                onClick={() => onTabChange(tab.id)}
                className={clx(
                  "destination-tab flex items-center gap-2 px-4 md:px-6 py-3 md:py-4 text-sm font-medium transition-all duration-300 whitespace-nowrap relative rounded-t-lg mx-1",
                  "hover:text-foreground",
                  "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
                  "min-w-0 flex-shrink-0",
                  isActive
                    ? "destination-tab-active text-white bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 shadow-xl shadow-blue-500/30 border-b-4 border-blue-400 transform -translate-y-1 ring-2 ring-blue-300/50 ring-offset-2 ring-offset-white backdrop-blur-sm"
                    : "destination-tab-inactive text-muted-foreground bg-gray-50/80 hover:bg-white hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300 hover:shadow-md"
                )}
              >
                <Icon className="w-4 h-4 flex-shrink-0" />
                <span className="hidden sm:inline">{tab.label}</span>
                <span className="sm:hidden text-xs">{tab.label.slice(0, 3)}</span>
                {tab.count !== undefined && (
                  <span
                    className={clx(
                      "destination-tab-badge ml-2 px-2.5 py-1 text-xs rounded-full flex-shrink-0 font-bold transition-all duration-300 border",
                      isActive
                        ? "bg-white/25 text-white shadow-lg backdrop-blur-sm border-white/30 ring-1 ring-white/20"
                        : "bg-gray-200/80 text-gray-600 hover:bg-gray-300 border-gray-300/50 hover:shadow-sm"
                    )}
                  >
                    {tab.count}
                  </span>
                )}
              </button>
            );
          })}
        </nav>
      </div>
    </div>
  );
};

export default DestinationTabs;
